# iroh + tarpc 集成架构文档

## 概述

本项目实现了 iroh P2P 网络与 tarpc RPC 框架的深度集成，通过 `duplex_stream` 作为桥接层，实现了类型安全的分布式 RPC 调用。

## 核心组件

### 1. duplex_stream - 桥接层

`duplex_stream` 是连接 iroh 和 tarpc 的关键桥接组件：

```rust
#[derive(Debug)]
#[pin_project::pin_project]
pub struct Duplex<R, W> {
    #[pin]
    reader: R,
    #[pin]
    writer: W,
}
```

**作用**：
- 将 iroh 的 `SendStream` 和 `RecvStream` 组合成单一的双向流
- 实现 `AsyncRead` 和 `AsyncWrite` trait，满足 tarpc 传输层要求
- 提供正确的异步 pinning 语义

### 2. TTSServer - 服务端实现

增强的 `TTSServer` 现在包含 node 信息：

```rust
#[derive(Clone)]
pub struct TTSServer {
    pub tts_speaker: Arc<Mutex<dyn TssInfer>>,
    pub node_id: Option<iroh::NodeId>,
    pub endpoint: Option<Arc<Endpoint>>,
}
```

**新增功能**：
- `with_node_info()` - 创建包含 node 信息的服务器实例
- `node_id()` - 获取当前节点 ID
- `endpoint()` - 获取 endpoint 引用
- 在 RPC 处理中可访问 node 信息

### 3. 协议处理流程

#### 服务端流程：
1. **创建 iroh endpoint** - 配置 P2P 网络参数
2. **获取 node 信息** - 提取 NodeId 和 NodeAddr
3. **创建 TTSServer** - 使用 `with_node_info()` 传递 node 信息
4. **注册协议处理器** - 通过 `iroh::protocol::Router` 注册 ALPN
5. **接受连接** - `ProtocolHandler::accept()` 处理入站连接
6. **创建 duplex_stream** - 桥接 iroh 流到 tarpc 传输层
7. **启动 tarpc 服务** - 在桥接传输层上运行 RPC 服务

#### 客户端流程：
1. **创建客户端 endpoint** - 配置客户端网络参数
2. **连接到服务端** - 使用 `NodeAddr` 建立 P2P 连接
3. **创建双向流** - 通过 `connection.open_bi()` 获取流
4. **创建 duplex_stream** - 桥接流到 tarpc 客户端
5. **创建 tarpc 客户端** - 在桥接传输层上创建类型安全客户端
6. **执行 RPC 调用** - 进行远程过程调用

## 数据流图

```
客户端                    网络                     服务端
┌─────────────┐          ┌─────┐          ┌─────────────┐
│ tarpc       │          │     │          │ tarpc       │
│ Client      │◄────────►│ P2P │◄────────►│ Server      │
└─────────────┘          │     │          └─────────────┘
       ▲                 └─────┘                 ▲
       │                                         │
┌─────────────┐                          ┌─────────────┐
│ duplex_     │                          │ duplex_     │
│ stream      │                          │ stream      │
└─────────────┘                          └─────────────┘
       ▲                                         ▲
       │                                         │
┌─────────────┐                          ┌─────────────┐
│ iroh        │                          │ iroh        │
│ SendStream  │                          │ RecvStream  │
│ RecvStream  │                          │ SendStream  │
└─────────────┘                          └─────────────┘
```

## 关键优势

### 1. 类型安全
- tarpc 提供编译时类型检查
- 自动生成客户端和服务端代码
- 防止 RPC 接口不匹配错误

### 2. P2P 网络能力
- 自动 NAT 穿透
- 本地网络发现
- 去中心化架构
- 支持多种传输协议

### 3. 性能优化
- 零拷贝序列化（使用 Bincode）
- 异步 I/O
- 连接复用
- 流式传输支持

### 4. 可扩展性
- 支持多种 ALPN 协议
- 可插拔的传输层
- 灵活的服务发现机制

## 使用示例

### 定义 RPC 服务

```rust
#[tarpc::service]
pub trait TTS {
    async fn tts(opt: TtsOpt) -> WavBytes;
}
```

### 服务端实现

```rust
let server_endpoint = Endpoint::builder()
    .secret_key(secret_key)
    .discovery_local_network()
    .alpns(vec![TEXT_TO_SPEAK_ALPN.to_vec()])
    .bind()
    .await?;

let server_node = server_endpoint.node_addr().await?;
let server = TTSServer::with_node_info(
    MyTTSImpl,
    server_node.node_id,
    Arc::new(server_endpoint.clone()),
);

let _relay = iroh::protocol::Router::builder(server_endpoint)
    .accept(TEXT_TO_SPEAK_ALPN, Arc::new(server))
    .spawn();
```

### 客户端调用

```rust
let client_endpoint = Endpoint::builder()
    .discovery_local_network()
    .bind()
    .await?;

let tts_client = create_tts_client(&client_endpoint, server_node).await?;
let response = tts_client.tts(context::current(), request).await?;
```

## 错误处理

项目使用 `anyhow::Result` 进行统一错误处理：
- 网络连接错误
- 序列化/反序列化错误  
- RPC 调用超时
- 节点发现失败

## 性能考虑

1. **连接池** - 复用 P2P 连接减少握手开销
2. **批量处理** - 支持批量 RPC 调用
3. **流式传输** - 大数据传输使用流式接口
4. **压缩** - 可选的数据压缩支持

## 安全性

1. **加密传输** - iroh 提供端到端加密
2. **身份验证** - 基于公钥的节点身份验证
3. **访问控制** - 可配置的服务访问策略
