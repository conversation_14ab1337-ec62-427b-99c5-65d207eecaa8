// 示例：iroh 配合 tarpc 使用，展示 node 信息传递和 duplex_stream 桥接
use std::sync::{Arc, Mutex};
use std::str::FromStr;

use anyhow::Result;
use iroh::{Endpoint, SecretKey};
use auto_live_common::service::tts::{TTSServer, create_tts_client, TEXT_TO_SPEAK_ALPN};
use auto_live_common::service::infer::{TssInfer, TtsOpt, WavBytes};
use tarpc::context;

// 示例 TTS 实现
struct ExampleTTS;

impl TssInfer for ExampleTTS {
    fn tts(&self, opt: TtsOpt) -> WavBytes {
        println!("处理 TTS 请求: {}", opt.text);
        // 这里应该是实际的 TTS 处理逻辑
        vec![0u8; 1024] // 模拟音频数据
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== iroh + tarpc 集成示例 ===\n");

    // 1. 创建服务端 endpoint
    println!("1. 创建服务端 endpoint...");
    let server_endpoint = Endpoint::builder()
        .secret_key(SecretKey::from_str(
            "a16d9ac4cedf36b7c4cc2e35264251d5b00426f9e92a2811eab8ed6cf27325e3",
        )?)
        .discovery_local_network()
        .alpns(vec![TEXT_TO_SPEAK_ALPN.to_vec()])
        .bind()
        .await?;

    // 2. 获取 node 信息
    let server_node = server_endpoint.node_addr().await?;
    println!("服务端 Node ID: {}", server_node.node_id);
    println!("服务端地址: {:?}\n", server_node);

    // 3. 创建带有 node 信息的 TTSServer
    println!("2. 创建 TTS 服务器（包含 node 信息）...");
    let server = TTSServer::with_node_info(
        ExampleTTS,
        server_node.node_id,
        Arc::new(server_endpoint.clone()),
    );

    // 4. 启动服务端
    println!("3. 启动 iroh 协议路由器...");
    let _relay = iroh::protocol::Router::builder(server_endpoint.clone())
        .accept(TEXT_TO_SPEAK_ALPN, Arc::new(server))
        .spawn();

    // 5. 创建客户端 endpoint
    println!("4. 创建客户端 endpoint...");
    let client_endpoint = Endpoint::builder()
        .discovery_local_network()
        .bind()
        .await?;

    let client_node = client_endpoint.node_addr().await?;
    println!("客户端 Node ID: {}", client_node.node_id);

    // 6. 等待一下让服务端完全启动
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 7. 创建 tarpc 客户端（通过 duplex_stream 桥接）
    println!("5. 创建 tarpc 客户端（通过 duplex_stream 桥接）...");
    let tts_client = create_tts_client(&client_endpoint, server_node).await?;

    // 8. 进行 RPC 调用
    println!("6. 执行 RPC 调用...\n");
    
    let request = TtsOpt {
        text: "Hello, 这是一个测试消息！".to_string(),
        reference_audio: None,
    };

    println!("发送 TTS 请求: {}", request.text);
    let response = tts_client.tts(context::current(), request).await?;
    println!("收到响应，音频数据长度: {} 字节\n", response.len());

    // 9. 展示架构优势
    println!("=== 架构优势展示 ===");
    println!("✓ duplex_stream 成功桥接 iroh 双向流到 tarpc 传输层");
    println!("✓ node 信息已传递给 tarpc 服务，可在处理中访问");
    println!("✓ 类型安全的 RPC 调用通过 P2P 网络完成");
    println!("✓ 支持本地网络发现和 NAT 穿透");

    Ok(())
}
